# 多阶段构建
# 第一阶段：构建应用
FROM node:18-alpine as build-stage

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用（测试环境）
RUN npm run build:test

# 第二阶段：Nginx 服务
FROM nginx:alpine

# 复制自定义 nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 复制构建好的文件到 nginx 目录
COPY --from=build-stage /app/dist /var/www/html/fe-water-baseline-base

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 暴露端口
EXPOSE 8923

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
