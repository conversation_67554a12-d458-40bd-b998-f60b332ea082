#!/bin/bash

# 部署脚本 - fe-water-baseline-base

set -e

echo "开始部署 fe-water-baseline-base..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="fe-water-baseline-base"
NGINX_CONF_PATH="/etc/nginx/sites-available/${PROJECT_NAME}"
NGINX_ENABLED_PATH="/etc/nginx/sites-enabled/${PROJECT_NAME}"
WEB_ROOT="/var/www/html/${PROJECT_NAME}"
BACKUP_DIR="/var/backups/${PROJECT_NAME}"

# 函数：打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 安装 Nginx（如果未安装）
install_nginx() {
    if ! command -v nginx &> /dev/null; then
        print_message "安装 Nginx..."
        apt update
        apt install -y nginx
    else
        print_message "Nginx 已安装"
    fi
}

# 创建备份
create_backup() {
    if [ -d "$WEB_ROOT" ]; then
        print_message "创建备份..."
        mkdir -p "$BACKUP_DIR"
        tar -czf "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S).tar.gz" -C "$WEB_ROOT" .
        print_message "备份完成: $BACKUP_DIR"
    fi
}

# 部署应用
deploy_app() {
    print_message "构建应用..."
    npm run build:test
    
    print_message "部署静态文件..."
    mkdir -p "$WEB_ROOT"
    cp -r dist/* "$WEB_ROOT/"
    
    # 设置权限
    chown -R www-data:www-data "$WEB_ROOT"
    chmod -R 755 "$WEB_ROOT"
}

# 配置 Nginx
configure_nginx() {
    print_message "配置 Nginx..."
    
    # 复制配置文件
    cp nginx.conf "$NGINX_CONF_PATH"
    
    # 更新配置文件中的路径
    sed -i "s|/var/www/html/fe-water-baseline-base|$WEB_ROOT|g" "$NGINX_CONF_PATH"
    
    # 启用站点
    ln -sf "$NGINX_CONF_PATH" "$NGINX_ENABLED_PATH"
    
    # 测试配置
    if nginx -t; then
        print_message "Nginx 配置测试通过"
    else
        print_error "Nginx 配置测试失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    print_message "重启 Nginx..."
    systemctl reload nginx
    systemctl enable nginx
    
    if systemctl is-active --quiet nginx; then
        print_message "Nginx 启动成功"
    else
        print_error "Nginx 启动失败"
        exit 1
    fi
}

# 主函数
main() {
    print_message "开始部署 ${PROJECT_NAME}..."
    
    check_root
    install_nginx
    create_backup
    deploy_app
    configure_nginx
    restart_services
    
    print_message "部署完成！"
    print_message "访问地址: http://172.16.200.11:8923"
    print_message "日志位置: /var/log/nginx/${PROJECT_NAME}.*.log"
}

# 执行主函数
main "$@"
